import React, { createContext, useContext, ReactNode } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api';
import { useAuth } from '@/contexts/AuthContext';
import type { Movie } from 'bookmarked-types';

// Types for the context
interface MediaContextType {
  // Movies
  movies: Movie[];
  isLoadingMovies: boolean;
  moviesError: Error | null;
  addMovie: (movie: Omit<Movie, '_id' | 'userId' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateMovie: (id: string, movie: Partial<Movie>) => Promise<void>;
  deleteMovie: (id: string) => Promise<void>;
  
  // Books (placeholder for future implementation)
  books: any[];
  isLoadingBooks: boolean;
  booksError: Error | null;
}

// Create context
const MediaContext = createContext<MediaContextType | undefined>(undefined);

// Provider props
interface MediaProviderProps {
  children: ReactNode;
}

// Media provider component
export const MediaProvider: React.FC<MediaProviderProps> = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const queryClient = useQueryClient();

  // Movies queries and mutations
  const {
    data: movies = [],
    isLoading: isLoadingMovies,
    error: moviesError,
  } = useQuery({
    queryKey: ['movies'],
    queryFn: async () => {
      const response = await apiClient.get('/movies');
      return response.data.data || [];
    },
    enabled: isAuthenticated,
  });

  const addMovieMutation = useMutation({
    mutationFn: async (movie: Omit<Movie, '_id' | 'userId' | 'createdAt' | 'updatedAt'>) => {
      const response = await apiClient.post('/movies', movie);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['movies'] });
    },
  });

  const updateMovieMutation = useMutation({
    mutationFn: async ({ id, movie }: { id: string; movie: Partial<Movie> }) => {
      const response = await apiClient.put(`/movies/${id}`, movie);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['movies'] });
    },
  });

  const deleteMovieMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await apiClient.delete(`/movies/${id}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['movies'] });
    },
  });

  // Wrapper functions for mutations
  const addMovie = async (movie: Omit<Movie, '_id' | 'userId' | 'createdAt' | 'updatedAt'>) => {
    await addMovieMutation.mutateAsync(movie);
  };

  const updateMovie = async (id: string, movie: Partial<Movie>) => {
    await updateMovieMutation.mutateAsync({ id, movie });
  };

  const deleteMovie = async (id: string) => {
    await deleteMovieMutation.mutateAsync(id);
  };

  // Context value
  const value: MediaContextType = {
    // Movies
    movies,
    isLoadingMovies,
    moviesError: moviesError as Error | null,
    addMovie,
    updateMovie,
    deleteMovie,
    
    // Books (placeholder)
    books: [],
    isLoadingBooks: false,
    booksError: null,
  };

  return (
    <MediaContext.Provider value={value}>
      {children}
    </MediaContext.Provider>
  );
};

// Hook to use media context
export const useMedia = (): MediaContextType => {
  const context = useContext(MediaContext);

  if (context === undefined) {
    throw new Error('useMedia must be used within a MediaProvider');
  }

  return context;
};

export default MediaContext;
